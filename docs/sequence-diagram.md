# Scrum Poker Online - Component Interaction Sequence Diagram

This diagram shows the interaction between components when a user joins a planning room.

## Room Joining Flow

```mermaid
sequenceDiagram
    participant User
    participant Browser
    participant RoomPage as RoomPage Component
    participant User<PERSON>oin<PERSON><PERSON> as UserJoinForm Component
    participant useSocket as useSocket Hook
    participant SocketClient as Socket.io Client
    participant SocketServer as Socket.io Server
    participant RoomStore as In-Memory Room Store

    Note over User, RoomStore: User navigates to /room/abc123

    User->>Browser: Navigate to /room/abc123
    Browser->>RoomPage: Mount component
    
    Note over RoomPage: Component initialization
    RoomPage->>useSocket: Call useSocket()
    
    Note over useSocket: useEffect runs (empty dependency array)
    useSocket->>useSocket: console.log('Initializing socket connection...')
    useSocket->>SocketClient: io({ path: '/api/socket' })
    
    SocketClient->>SocketServer: WebSocket connection request
    SocketServer->>SocketClient: Connection established
    SocketClient->>useSocket: emit('connect')
    useSocket->>useSocket: setIsConnected(true)
    useSocket->>RoomPage: Return socket state
    
    Note over RoomPage: Check if user has joined
    RoomPage->>UserJoinForm: Render (hasJoined = false)
    UserJoinForm->>User: Display join form
    
    Note over User, UserJoinForm: User enters name and submits
    User->>UserJoinForm: Enter name "Alice"
    User->>UserJoinForm: Click "Join Room"
    
    UserJoinForm->>UserJoinForm: handleSubmit()
    UserJoinForm->>RoomPage: onJoin("Alice")
    RoomPage->>RoomPage: handleJoinRoom("Alice")
    RoomPage->>useSocket: joinRoom("abc123", "Alice")
    
    useSocket->>SocketClient: emit('room:join', "abc123", "Alice")
    SocketClient->>SocketServer: Send room:join event
    
    Note over SocketServer: Process join request
    SocketServer->>SocketServer: Generate userId = uuid()
    SocketServer->>SocketServer: socket.data = { userId, userName, roomId }
    SocketServer->>SocketServer: socket.join("abc123")
    
    alt Room doesn't exist
        SocketServer->>RoomStore: Create new room
        RoomStore->>SocketServer: Room created
    else Room exists
        SocketServer->>RoomStore: Get existing room
        RoomStore->>SocketServer: Return room data
    end
    
    SocketServer->>RoomStore: Add user to room
    RoomStore->>SocketServer: User added
    
    Note over SocketServer: Notify all participants
    SocketServer->>SocketClient: emit('room:updated', room) [to all in room]
    SocketServer->>SocketClient: emit('user:joined', user) [to others in room]
    
    SocketClient->>useSocket: Receive 'room:updated'
    useSocket->>useSocket: setRoom(updatedRoom)
    useSocket->>RoomPage: Update room state
    
    RoomPage->>RoomPage: setHasJoined(true)
    
    Note over RoomPage: User successfully joined
    RoomPage->>RoomPage: Render main room interface
    RoomPage->>User: Show room with stories, estimation area, participants
```

## Real-time Story Estimation Flow

```mermaid
sequenceDiagram
    participant UserA as User A (Alice)
    participant UserB as User B (Bob)
    participant StoryList as StoryList Component
    participant EstimationArea as EstimationArea Component
    participant useSocketA as useSocket Hook A
    participant useSocketB as useSocket Hook B
    participant SocketServer as Socket.io Server
    participant RoomStore as In-Memory Room Store

    Note over UserA, RoomStore: Both users are in the same room

    UserA->>StoryList: Click "Add Story"
    StoryList->>StoryList: Show add story form
    UserA->>StoryList: Enter story details
    UserA->>StoryList: Submit story
    
    StoryList->>useSocketA: addStory(roomId, title, description)
    useSocketA->>SocketServer: emit('story:add', roomId, storyData)
    
    SocketServer->>RoomStore: Add story to room
    RoomStore->>SocketServer: Story added
    
    SocketServer->>useSocketA: emit('story:added', story)
    SocketServer->>useSocketB: emit('story:added', story)
    SocketServer->>useSocketA: emit('room:updated', room)
    SocketServer->>useSocketB: emit('room:updated', room)
    
    useSocketA->>StoryList: Update stories list
    useSocketB->>StoryList: Update stories list
    
    Note over UserA, UserB: Both see the new story

    UserA->>StoryList: Click on story to select
    StoryList->>useSocketA: selectStory(roomId, storyId)
    useSocketA->>SocketServer: emit('story:select', roomId, storyId)
    
    SocketServer->>RoomStore: Set currentStoryId
    SocketServer->>useSocketA: emit('room:updated', room)
    SocketServer->>useSocketB: emit('room:updated', room)
    
    useSocketA->>EstimationArea: Show estimation for selected story
    useSocketB->>EstimationArea: Show estimation for selected story
    
    Note over UserA, UserB: Both users estimate

    UserA->>EstimationArea: Select estimate "5"
    EstimationArea->>useSocketA: submitEstimate(roomId, storyId, 5)
    useSocketA->>SocketServer: emit('estimate:submit', roomId, storyId, 5)
    
    UserB->>EstimationArea: Select estimate "8"
    EstimationArea->>useSocketB: submitEstimate(roomId, storyId, 8)
    useSocketB->>SocketServer: emit('estimate:submit', roomId, storyId, 8)
    
    SocketServer->>RoomStore: Store estimates
    SocketServer->>useSocketA: emit('room:updated', room)
    SocketServer->>useSocketB: emit('room:updated', room)
    
    Note over EstimationArea: Show estimation progress

    UserA->>EstimationArea: Click "Reveal Estimates"
    EstimationArea->>useSocketA: revealEstimates(roomId, storyId)
    useSocketA->>SocketServer: emit('estimates:reveal', roomId, storyId)
    
    SocketServer->>RoomStore: Set story.isRevealed = true
    SocketServer->>useSocketA: emit('estimates:revealed', storyId)
    SocketServer->>useSocketB: emit('estimates:revealed', storyId)
    SocketServer->>useSocketA: emit('room:updated', room)
    SocketServer->>useSocketB: emit('room:updated', room)
    
    useSocketA->>EstimationArea: Show revealed estimates & statistics
    useSocketB->>EstimationArea: Show revealed estimates & statistics
    
    Note over UserA, UserB: Both see estimates: Alice=5, Bob=8, Mean=6.5, Median=6.5
```

## Component Lifecycle and Socket Management

```mermaid
sequenceDiagram
    participant Browser
    participant RoomPage as RoomPage Component
    participant useSocket as useSocket Hook
    participant SocketClient as Socket.io Client
    participant SocketServer as Socket.io Server

    Note over Browser, SocketServer: Component Mount Lifecycle

    Browser->>RoomPage: Mount component
    RoomPage->>useSocket: useSocket() called
    
    Note over useSocket: useEffect with empty deps [] runs once
    useSocket->>useSocket: console.log('Initializing socket connection...')
    useSocket->>SocketClient: Create new socket instance
    useSocket->>SocketClient: Setup event listeners
    
    SocketClient->>SocketServer: Connect
    SocketServer->>SocketClient: Connection established
    SocketClient->>useSocket: 'connect' event
    useSocket->>useSocket: setIsConnected(true)
    
    Note over RoomPage: Component renders with socket ready

    Note over Browser, SocketServer: User Navigation Away

    Browser->>RoomPage: Unmount component
    RoomPage->>useSocket: Cleanup function runs
    useSocket->>SocketClient: socketInstance.disconnect()
    SocketClient->>SocketServer: Disconnect
    SocketServer->>SocketServer: Handle user disconnect
    
    Note over SocketServer: Clean up user from rooms
```

## Key Points

1. **useEffect Timing**: Runs once on component mount due to empty dependency array `[]`
2. **Socket Persistence**: One socket connection per component lifecycle
3. **Real-time Updates**: All participants receive updates simultaneously
4. **State Management**: Room state is managed server-side and synchronized to all clients
5. **Cleanup**: Socket disconnects automatically when component unmounts
