import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        // Primary color: Medium spring bud (#B4F46F)
        primary: {
          50: '#f4fce8',
          100: '#e6f7cc',
          200: '#d0f0a0',
          300: '#b4f46f', // Main primary color
          400: '#9ae84a',
          500: '#7dd32a',
          600: '#5fb01e',
          700: '#4a8a1a',
          800: '#3d6e1a',
          900: '#345c1a',
        },
        // Background color: Off-white (#F0F4EF)
        background: {
          DEFAULT: '#F0F4EF',
          light: '#f8faf7',
          dark: '#e8ece7',
        },
        // Accent color: Celadon (#ACE1AF)
        accent: {
          50: '#f2faf3',
          100: '#e1f4e3',
          200: '#c4e8c9',
          300: '#ace1af', // Main accent color
          400: '#7bc982',
          500: '#5aad63',
          600: '#458b4c',
          700: '#3a6f3f',
          800: '#325836',
          900: '#2b482e',
        },
        // Additional semantic colors
        success: '#22c55e',
        warning: '#f59e0b',
        error: '#ef4444',
        info: '#3b82f6',
      },
      fontFamily: {
        // Headline font: Space Grotesk
        headline: ['Space Grotesk', 'sans-serif'],
        // Body font: Inter
        body: ['Inter', 'sans-serif'],
        sans: ['Inter', 'sans-serif'], // Default sans-serif
      },
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'pulse-soft': 'pulseSoft 2s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        pulseSoft: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.7' },
        },
      },
      boxShadow: {
        'soft': '0 2px 8px rgba(0, 0, 0, 0.1)',
        'medium': '0 4px 16px rgba(0, 0, 0, 0.15)',
        'strong': '0 8px 32px rgba(0, 0, 0, 0.2)',
      },
      borderRadius: {
        'xl': '1rem',
        '2xl': '1.5rem',
      },
    },
  },
  plugins: [],
};

export default config;
