# Scrum Poker Online

A collaborative story point estimation tool for agile teams, built with Next.js, Socket.io, and Tailwind CSS.

## Features

- **Real-time Collaboration**: Join planning rooms with unique URLs and estimate stories together
- **User Identification**: Enter your name to participate and track estimates by team member
- **Story Management**: Add, view, and manage user stories with descriptions
- **Planning Poker Cards**: Use standard <PERSON><PERSON><PERSON><PERSON> sequence for estimation (0, 0.5, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89)
- **Confidential Estimation**: Submit estimates privately until everyone has voted
- **Automatic Statistics**: Calculate and display mean, median, and mode of estimates
- **Divergence Detection**: Identify when estimates vary significantly
- **Smart Probing Questions**: Generate contextual questions to help reach consensus
- **Clean UI**: Professional design with custom color scheme and typography

## Tech Stack

- **Frontend**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS with custom design system
- **Real-time**: Socket.io for WebSocket communication
- **Icons**: Lucide React
- **Testing**: Jest with Testing Library
- **Fonts**: Space Grotesk (headlines) and Inter (body text)
- **Fonts**: Google Fonts


## Design System

- **Primary Color**: Medium Spring Bud (#B4F46F) - for professionalism and clarity
- **Background**: Off-white (#F0F4EF) - clean, focused workspace
- **Accent**: Celadon (#ACE1AF) - highlights for user interactions
- **Typography**: Space Grotesk for headlines, Inter for body text
- **Layout**: Clean, grid-based with clear section separation

## Getting Started

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Run the development server**:
   ```bash
   npm run dev
   ```

3. **Open your browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

## Quick Start Guide

### Creating Your First Planning Session

1. **Start the Application**:
   - Open your browser and go to `http://localhost:3000`
   - You'll see the Scrum Poker Online homepage

2. **Create a New Room**:
   - Click the "Create New Room" button
   - You'll be redirected to a unique room URL (e.g., `/room/abc12345`)

3. **Join the Room**:
   - Enter your name in the "Join Planning Room" form
   - Click "Join Room" to enter the planning session

4. **Add Your First Story**:
   - In the Stories panel, click "Add Story"
   - Enter a story title (e.g., "As a user, I want to login")
   - Optionally add a description with acceptance criteria
   - Click "Add Story"

5. **Start Estimating**:
   - Click on the story you just added to select it
   - Choose your estimate using the planning poker cards (0, 0.5, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89)
   - Your estimate is submitted confidentially

6. **Reveal Results**:
   - Once you've estimated, click "Reveal Estimates"
   - View the statistical analysis (mean, median, mode)

### Testing Real-time Collaboration

To experience the full collaborative features:

1. **Open Multiple Browser Sessions**:
   - Open 2-3 browser tabs or windows
   - Navigate to `http://localhost:3000` in each

2. **Join the Same Room**:
   - Use the room ID from your first session
   - Enter different names for each participant (e.g., "Alice", "Bob", "Charlie")
   - Join the same room from all sessions

3. **Collaborate in Real-time**:
   - **Add Stories**: Add stories from any session and watch them appear instantly in all others
   - **Select Stories**: When one person selects a story, all participants see the current estimation target
   - **Submit Estimates**: Each person can submit their estimate privately
   - **Track Progress**: Watch the estimation progress bar update as each person votes
   - **Reveal Together**: Any participant can reveal estimates for everyone

4. **Test Advanced Features**:
   - **Divergent Estimates**: Have participants submit very different estimates (e.g., 1, 13, 34)
   - **Probing Questions**: Click "Get Questions" to see smart questions for reaching consensus
   - **Re-estimation**: Use "Reset" to clear estimates and vote again after discussion

5. **User Presence**:
   - Watch the Participants panel show who's online
   - See real-time connection status indicators
   - Notice when users join or leave the room

### Tips for Effective Sessions

- **Story Preparation**: Add all stories before starting estimation
- **Discussion**: Use probing questions when estimates vary significantly
- **Consensus**: Re-estimate after discussing divergent views
- **Room Sharing**: Share the room URL with team members to join remotely

## Usage

1. **Create a Room**: Click "Create New Room" on the homepage
2. **Join a Room**: Enter a room ID to join an existing session
3. **Add Stories**: Use the story list to add user stories with descriptions
4. **Estimate**: Select a story and choose your estimate using planning poker cards
5. **Reveal**: Once everyone has voted, reveal estimates to see the results
6. **Discuss**: Use probing questions if estimates diverge significantly
7. **Consensus**: Re-estimate after discussion to reach agreement

## Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm test` - Run Jest tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report

## Project Structure

```
src/
├── app/                 # Next.js app directory
│   ├── api/socket/     # Socket.io API route
│   ├── room/[roomId]/  # Dynamic room pages
│   └── globals.css     # Global styles
├── components/         # React components
├── hooks/             # Custom React hooks
├── types/             # TypeScript type definitions
└── utils/             # Utility functions and statistics
```

## Testing

The project includes comprehensive tests for the statistics utilities:

```bash
npm test
```

Tests cover:
- Statistical calculations (mean, median, mode)
- Divergence detection algorithms
- Probing question generation
- Fibonacci number utilities

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License.
