'use client';

import { useState } from 'react';
import { Copy, Check, LogOut, Users } from 'lucide-react';
import type { Room, User } from '@/types';

interface RoomHeaderProps {
  room: Room;
  currentUser: User;
  onLeaveRoom: () => void;
}

export default function RoomHeader({ room, currentUser, onLeaveRoom }: RoomHeaderProps) {
  const [copied, setCopied] = useState(false);

  const copyRoomUrl = async () => {
    const url = `${window.location.origin}/room/${room.id}`;
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy URL:', err);
    }
  };

  return (
    <div className="bg-white rounded-2xl shadow-soft p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center justify-center w-12 h-12 bg-primary-300 rounded-xl">
            <Users className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-headline font-bold text-gray-900">
              {room.name}
            </h1>
            <div className="flex items-center gap-2 mt-1">
              <span className="text-sm text-gray-500">Room ID:</span>
              <code className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                {room.id}
              </code>
              <button
                onClick={copyRoomUrl}
                className="p-1 hover:bg-gray-100 rounded transition-colors"
                title="Copy room URL"
              >
                {copied ? (
                  <Check className="w-4 h-4 text-green-600" />
                ) : (
                  <Copy className="w-4 h-4 text-gray-400" />
                )}
              </button>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <div className="text-right">
            <p className="text-sm font-medium text-gray-900">
              Welcome, {currentUser.name}
            </p>
            <p className="text-xs text-gray-500">
              {room.users.length} participant{room.users.length !== 1 ? 's' : ''}
            </p>
          </div>
          <button
            onClick={onLeaveRoom}
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
          >
            <LogOut className="w-4 h-4" />
            Leave
          </button>
        </div>
      </div>

      {room.stories.length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-100">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">
              {room.stories.length} stor{room.stories.length !== 1 ? 'ies' : 'y'} added
            </span>
            {room.currentStoryId && (
              <span className="text-primary-600 font-medium">
                Currently estimating
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
