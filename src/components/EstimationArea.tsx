'use client';

import { useState } from 'react';
import { Eye, RotateCcw, HelpCircle, Calculator } from 'lucide-react';
import type { Story, User } from '@/types';
import { POKER_CARDS } from '@/types';
import { calculateEstimateStats, hasSignificantDivergence } from '@/utils/statistics';

interface EstimationAreaProps {
  story: Story;
  users: User[];
  currentUser: User;
  onSubmitEstimate: (storyId: string, estimate: number | null) => void;
  onRevealEstimates: (storyId: string) => void;
  onResetEstimates: (storyId: string) => void;
  onRequestProbing: (storyId: string) => void;
}

export default function EstimationArea({
  story,
  users,
  currentUser,
  onSubmitEstimate,
  onRevealEstimates,
  onResetEstimates,
  onRequestProbing,
}: EstimationAreaProps) {
  const [selectedEstimate, setSelectedEstimate] = useState<number | null>(
    story.estimates[currentUser.id] || null
  );

  const handleEstimateSelect = (estimate: number | null) => {
    setSelectedEstimate(estimate);
    onSubmitEstimate(story.id, estimate);
  };

  const getEstimateCount = () => {
    return Object.values(story.estimates).filter(estimate => estimate !== null).length;
  };

  const getAllEstimates = () => {
    return Object.values(story.estimates).filter(estimate => estimate !== null) as number[];
  };

  const canReveal = () => {
    return getEstimateCount() > 0 && !story.isRevealed;
  };

  const stats = story.isRevealed ? calculateEstimateStats(getAllEstimates()) : null;
  const showDivergenceWarning = story.isRevealed && hasSignificantDivergence(getAllEstimates());

  return (
    <div className="bg-white rounded-2xl shadow-soft p-6">
      {/* Story Header */}
      <div className="mb-6">
        <h3 className="text-lg font-headline font-semibold text-gray-900 mb-2">
          {story.title}
        </h3>
        {story.description && (
          <p className="text-sm text-gray-600 leading-relaxed">
            {story.description}
          </p>
        )}
      </div>

      {/* Estimation Status */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Calculator className="w-4 h-4 text-gray-600" />
            <span className="text-sm font-medium text-gray-700">
              Estimation Progress
            </span>
          </div>
          <span className="text-sm text-gray-600">
            {getEstimateCount()}/{users.length} votes
          </span>
        </div>
        
        <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-primary-300 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(getEstimateCount() / users.length) * 100}%` }}
          />
        </div>
      </div>

      {/* Planning Poker Cards */}
      {!story.isRevealed && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">
            Select your estimate:
          </h4>
          <div className="grid grid-cols-4 gap-2">
            {POKER_CARDS.map((value) => (
              <button
                key={value}
                onClick={() => handleEstimateSelect(value)}
                className={`aspect-[3/4] rounded-lg border-2 transition-all duration-200 flex items-center justify-center font-bold text-lg ${
                  selectedEstimate === value
                    ? 'border-primary-300 bg-primary-50 text-primary-700 scale-105'
                    : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50 text-gray-700'
                }`}
              >
                {value}
              </button>
            ))}
          </div>
          
          {/* Pass/Skip Option */}
          <button
            onClick={() => handleEstimateSelect(null)}
            className={`mt-2 w-full py-2 rounded-lg border-2 transition-all duration-200 text-sm font-medium ${
              selectedEstimate === null
                ? 'border-gray-400 bg-gray-100 text-gray-700'
                : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50 text-gray-600'
            }`}
          >
            Pass / Skip
          </button>
        </div>
      )}

      {/* Revealed Estimates */}
      {story.isRevealed && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">
            Estimates:
          </h4>
          <div className="space-y-2">
            {users.map((user) => {
              const estimate = story.estimates[user.id];
              return (
                <div
                  key={user.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-700">
                      {user.name.charAt(0).toUpperCase()}
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      {user.name}
                    </span>
                  </div>
                  <span className="text-sm font-bold text-gray-700">
                    {estimate !== null ? estimate : 'Pass'}
                  </span>
                </div>
              );
            })}
          </div>

          {/* Statistics */}
          {stats && stats.estimates.length > 0 && (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <h5 className="text-sm font-medium text-blue-900 mb-2">Statistics:</h5>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-blue-700 font-medium">Mean:</span>
                  <span className="ml-1 text-blue-900">{stats.mean}</span>
                </div>
                <div>
                  <span className="text-blue-700 font-medium">Median:</span>
                  <span className="ml-1 text-blue-900">{stats.median}</span>
                </div>
                <div>
                  <span className="text-blue-700 font-medium">Mode:</span>
                  <span className="ml-1 text-blue-900">
                    {stats.mode.length === 1 ? stats.mode[0] : stats.mode.join(', ')}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Divergence Warning */}
          {showDivergenceWarning && (
            <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start gap-3">
                <HelpCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">
                    Significant estimate divergence detected
                  </p>
                  <p className="text-xs text-yellow-700 mt-1">
                    Consider discussing the story requirements or breaking it down further.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex gap-2">
        {canReveal() && (
          <button
            onClick={() => onRevealEstimates(story.id)}
            className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-primary-300 hover:bg-primary-400 text-white font-medium rounded-lg transition-colors"
          >
            <Eye className="w-4 h-4" />
            Reveal Estimates
          </button>
        )}

        {story.isRevealed && (
          <>
            <button
              onClick={() => onResetEstimates(story.id)}
              className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium rounded-lg transition-colors"
            >
              <RotateCcw className="w-4 h-4" />
              Reset
            </button>

            {showDivergenceWarning && (
              <button
                onClick={() => onRequestProbing(story.id)}
                className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-accent-300 hover:bg-accent-400 text-white font-medium rounded-lg transition-colors"
              >
                <HelpCircle className="w-4 h-4" />
                Get Questions
              </button>
            )}
          </>
        )}
      </div>
    </div>
  );
}
