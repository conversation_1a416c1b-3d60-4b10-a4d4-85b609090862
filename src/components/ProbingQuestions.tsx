'use client';

import { useState, useEffect } from 'react';
import { HelpCircle, X, MessageCircle, AlertTriangle, Layers, Link } from 'lucide-react';
import type { ProbingQuestion } from '@/types';

interface ProbingQuestionsProps {
  questions: ProbingQuestion[];
  onClose: () => void;
}

const categoryIcons = {
  complexity: MessageCircle,
  uncertainty: AlertTriangle,
  dependencies: Link,
  scope: Layers,
};

const categoryColors = {
  complexity: 'bg-blue-50 border-blue-200 text-blue-800',
  uncertainty: 'bg-yellow-50 border-yellow-200 text-yellow-800',
  dependencies: 'bg-purple-50 border-purple-200 text-purple-800',
  scope: 'bg-green-50 border-green-200 text-green-800',
};

export default function ProbingQuestions({ questions, onClose }: ProbingQuestionsProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 200);
  };

  if (questions.length === 0) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div
        className={`bg-white rounded-2xl shadow-strong max-w-2xl w-full max-h-[80vh] overflow-hidden transition-all duration-200 ${
          isVisible ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
        }`}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-accent-300 rounded-xl flex items-center justify-center">
              <HelpCircle className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-headline font-semibold text-gray-900">
                Probing Questions
              </h3>
              <p className="text-sm text-gray-600">
                Questions to help reach consensus on estimates
              </p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          <div className="space-y-4">
            {questions.map((question) => {
              const Icon = categoryIcons[question.category];
              const colorClass = categoryColors[question.category];

              return (
                <div
                  key={question.id}
                  className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-start gap-3">
                    <div className={`p-2 rounded-lg border ${colorClass}`}>
                      <Icon className="w-4 h-4" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className={`text-xs font-medium px-2 py-1 rounded-full ${colorClass}`}>
                          {question.category.charAt(0).toUpperCase() + question.category.slice(1)}
                        </span>
                      </div>
                      <p className="text-sm text-gray-900 leading-relaxed">
                        {question.question}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Tips */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="text-sm font-medium text-blue-900 mb-2">
              💡 Discussion Tips:
            </h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Take turns sharing your perspective on each question</li>
              <li>• Focus on understanding different viewpoints</li>
              <li>• Consider breaking down the story if complexity is high</li>
              <li>• Re-estimate after discussion to see if consensus improves</li>
            </ul>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600">
              Discuss these questions with your team, then re-estimate
            </p>
            <button
              onClick={handleClose}
              className="px-4 py-2 bg-primary-300 hover:bg-primary-400 text-white font-medium rounded-lg transition-colors"
            >
              Got it
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
