'use client';

import { useState } from 'react';
import { Plus, BookOpen, Target, Users, Eye, EyeOff } from 'lucide-react';
import type { Story } from '@/types';

interface StoryListProps {
  stories: Story[];
  currentStoryId: string | null;
  onAddStory: (title: string, description: string) => void;
  onSelectStory: (storyId: string) => void;
}

export default function StoryList({ 
  stories, 
  currentStoryId, 
  onAddStory, 
  onSelectStory 
}: StoryListProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim()) return;

    onAddStory(title.trim(), description.trim());
    setTitle('');
    setDescription('');
    setShowAddForm(false);
  };

  const getEstimateCount = (story: Story) => {
    return Object.values(story.estimates).filter(estimate => estimate !== null).length;
  };

  const getTotalUsers = (story: Story) => {
    return Object.keys(story.estimates).length;
  };

  return (
    <div className="bg-white rounded-2xl shadow-soft p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <BookOpen className="w-5 h-5 text-gray-600" />
          <h3 className="text-lg font-headline font-semibold text-gray-900">
            Stories
          </h3>
          <span className="text-sm text-gray-500">({stories.length})</span>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-primary-600 hover:bg-primary-50 rounded-lg transition-colors"
        >
          <Plus className="w-4 h-4" />
          Add Story
        </button>
      </div>

      {/* Add Story Form */}
      {showAddForm && (
        <div className="mb-4 p-4 bg-gray-50 rounded-lg animate-slide-down">
          <form onSubmit={handleSubmit} className="space-y-3">
            <div>
              <label htmlFor="storyTitle" className="block text-sm font-medium text-gray-700 mb-1">
                Story Title
              </label>
              <input
                id="storyTitle"
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="As a user, I want to..."
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 focus:border-transparent text-sm"
                required
                autoFocus
              />
            </div>
            <div>
              <label htmlFor="storyDescription" className="block text-sm font-medium text-gray-700 mb-1">
                Description (optional)
              </label>
              <textarea
                id="storyDescription"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Additional details, acceptance criteria..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 focus:border-transparent text-sm resize-none"
              />
            </div>
            <div className="flex gap-2">
              <button
                type="submit"
                className="px-4 py-2 bg-primary-300 hover:bg-primary-400 text-white text-sm font-medium rounded-lg transition-colors"
              >
                Add Story
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowAddForm(false);
                  setTitle('');
                  setDescription('');
                }}
                className="px-4 py-2 text-gray-600 hover:bg-gray-100 text-sm font-medium rounded-lg transition-colors"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Stories List */}
      <div className="space-y-3">
        {stories.length === 0 ? (
          <div className="text-center py-8">
            <BookOpen className="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-500 text-sm">No stories yet</p>
            <p className="text-gray-400 text-xs mt-1">
              Add your first story to start estimating
            </p>
          </div>
        ) : (
          stories.map((story) => {
            const estimateCount = getEstimateCount(story);
            const totalUsers = getTotalUsers(story);
            const isSelected = story.id === currentStoryId;

            return (
              <div
                key={story.id}
                className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                  isSelected
                    ? 'border-primary-300 bg-primary-50'
                    : 'border-gray-200 hover:border-gray-300 bg-white hover:bg-gray-50'
                }`}
                onClick={() => onSelectStory(story.id)}
              >
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-gray-900 text-sm leading-tight">
                    {story.title}
                  </h4>
                  <div className="flex items-center gap-2 ml-2">
                    {story.isRevealed ? (
                      <Eye className="w-4 h-4 text-green-600" title="Estimates revealed" />
                    ) : (
                      <EyeOff className="w-4 h-4 text-gray-400" title="Estimates hidden" />
                    )}
                    {isSelected && (
                      <Target className="w-4 h-4 text-primary-600" title="Currently estimating" />
                    )}
                  </div>
                </div>

                {story.description && (
                  <p className="text-xs text-gray-600 mb-3 line-clamp-2">
                    {story.description}
                  </p>
                )}

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    <Users className="w-3 h-3" />
                    <span>
                      {estimateCount}/{totalUsers} estimated
                    </span>
                  </div>
                  
                  {story.isRevealed && estimateCount > 0 && (
                    <div className="text-xs text-gray-600">
                      Estimates: {Object.values(story.estimates)
                        .filter(e => e !== null)
                        .join(', ')}
                    </div>
                  )}
                </div>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
}
