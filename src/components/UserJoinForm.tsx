'use client';

import { useState } from 'react';
import { Users, LogIn } from 'lucide-react';

interface UserJoinFormProps {
  roomId: string;
  onJoin: (userName: string) => void;
  error?: string | null;
}

export default function UserJoinForm({ roomId, onJoin, error }: UserJoinFormProps) {
  const [userName, setUserName] = useState('');
  const [isJoining, setIsJoining] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!userName.trim()) return;

    setIsJoining(true);
    try {
      onJoin(userName.trim());
    } catch (err) {
      console.error('Failed to join room:', err);
    } finally {
      setIsJoining(false);
    }
  };

  return (
    <div className="bg-white rounded-2xl shadow-medium p-8 w-full max-w-md">
      <div className="text-center mb-6">
        <div className="inline-flex items-center justify-center w-12 h-12 bg-primary-300 rounded-xl mb-4">
          <Users className="w-6 h-6 text-white" />
        </div>
        <h2 className="text-2xl font-headline font-bold text-gray-900 mb-2">
          Join Planning Room
        </h2>
        <p className="text-gray-600 font-body">
          Room ID: <span className="font-mono font-medium text-primary-600">{roomId}</span>
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="userName" className="block text-sm font-medium text-gray-700 mb-2">
            Your Name
          </label>
          <input
            id="userName"
            type="text"
            value={userName}
            onChange={(e) => setUserName(e.target.value)}
            placeholder="Enter your name"
            className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-300 focus:border-transparent transition-colors"
            required
            disabled={isJoining}
            maxLength={50}
          />
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        <button
          type="submit"
          disabled={!userName.trim() || isJoining}
          className="w-full bg-primary-300 hover:bg-primary-400 disabled:bg-gray-200 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-xl transition-colors duration-200 flex items-center justify-center gap-2"
        >
          {isJoining ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Joining...
            </>
          ) : (
            <>
              <LogIn className="w-4 h-4" />
              Join Room
            </>
          )}
        </button>
      </form>

      <div className="mt-6 text-center">
        <p className="text-xs text-gray-500">
          By joining, you agree to participate in collaborative story estimation
        </p>
      </div>
    </div>
  );
}
