'use client';

import { User as UserIcon, Crown } from 'lucide-react';
import type { User } from '@/types';

interface UserPresenceProps {
  users: User[];
  currentUser: User;
}

export default function UserPresence({ users, currentUser }: UserPresenceProps) {
  const sortedUsers = [...users].sort((a, b) => {
    // Current user first, then alphabetical
    if (a.id === currentUser.id) return -1;
    if (b.id === currentUser.id) return 1;
    return a.name.localeCompare(b.name);
  });

  return (
    <div className="bg-white rounded-2xl shadow-soft p-6">
      <div className="flex items-center gap-2 mb-4">
        <UserIcon className="w-5 h-5 text-gray-600" />
        <h3 className="text-lg font-headline font-semibold text-gray-900">
          Participants
        </h3>
        <span className="text-sm text-gray-500">({users.length})</span>
      </div>

      <div className="space-y-3">
        {sortedUsers.map((user, index) => (
          <div
            key={user.id}
            className={`flex items-center gap-3 p-3 rounded-lg transition-colors ${
              user.id === currentUser.id
                ? 'bg-primary-50 border border-primary-200'
                : 'bg-gray-50 hover:bg-gray-100'
            }`}
          >
            <div className="relative">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  user.id === currentUser.id
                    ? 'bg-primary-300 text-white'
                    : 'bg-gray-300 text-gray-700'
                }`}
              >
                {user.name.charAt(0).toUpperCase()}
              </div>
              <div
                className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
                  user.isConnected ? 'bg-green-400' : 'bg-gray-400'
                }`}
                title={user.isConnected ? 'Online' : 'Offline'}
              />
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {user.name}
                </p>
                {user.id === currentUser.id && (
                  <span className="text-xs text-primary-600 font-medium">
                    (You)
                  </span>
                )}
                {index === 0 && users.length > 1 && (
                  <Crown className="w-3 h-3 text-yellow-500" title="Room creator" />
                )}
              </div>
              <p className="text-xs text-gray-500">
                {user.isConnected ? 'Online' : 'Offline'}
              </p>
            </div>

            <div className="flex items-center">
              <div
                className={`w-2 h-2 rounded-full ${
                  user.isConnected ? 'bg-green-400' : 'bg-gray-400'
                }`}
              />
            </div>
          </div>
        ))}
      </div>

      {users.length === 1 && (
        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-700 text-center">
            Share the room URL to invite team members
          </p>
        </div>
      )}
    </div>
  );
}
