export interface User {
  id: string;
  name: string;
  isConnected: boolean;
}

export interface Story {
  id: string;
  title: string;
  description: string;
  estimates: Record<string, number | null>; // userId -> estimate
  isRevealed: boolean;
  createdAt: Date;
}

export interface Room {
  id: string;
  name: string;
  users: User[];
  stories: Story[];
  currentStoryId: string | null;
  createdAt: Date;
}

export interface Estimate {
  userId: string;
  storyId: string;
  value: number | null;
}

export interface EstimateStats {
  mean: number;
  median: number;
  mode: number[];
  variance: number;
  standardDeviation: number;
  estimates: number[];
}

export interface ProbingQuestion {
  id: string;
  question: string;
  category: 'complexity' | 'uncertainty' | 'dependencies' | 'scope';
}

// Socket.io event types
export interface ServerToClientEvents {
  'room:updated': (room: Room) => void;
  'user:joined': (user: User) => void;
  'user:left': (userId: string) => void;
  'story:added': (story: Story) => void;
  'story:updated': (story: Story) => void;
  'estimate:submitted': (estimate: Estimate) => void;
  'estimates:revealed': (storyId: string) => void;
  'probing:questions': (questions: ProbingQuestion[]) => void;
  'error': (message: string) => void;
}

export interface ClientToServerEvents {
  'room:join': (roomId: string, userName: string) => void;
  'room:leave': (roomId: string) => void;
  'story:add': (roomId: string, story: Omit<Story, 'id' | 'estimates' | 'isRevealed' | 'createdAt'>) => void;
  'story:select': (roomId: string, storyId: string) => void;
  'estimate:submit': (roomId: string, storyId: string, estimate: number | null) => void;
  'estimates:reveal': (roomId: string, storyId: string) => void;
  'estimates:reset': (roomId: string, storyId: string) => void;
  'probing:request': (roomId: string, storyId: string) => void;
}

export interface InterServerEvents {
  ping: () => void;
}

export interface SocketData {
  userId: string;
  userName: string;
  roomId: string;
}

// Planning poker card values
export const POKER_CARDS = [0, 0.5, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89] as const;
export type PokerCard = typeof POKER_CARDS[number];
