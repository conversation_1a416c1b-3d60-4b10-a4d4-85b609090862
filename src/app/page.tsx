'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { v4 as uuidv4 } from 'uuid';
import { Users, Plus, ArrowRight } from 'lucide-react';

export default function Home() {
  const router = useRouter();
  const [roomId, setRoomId] = useState('');

  const createNewRoom = () => {
    const newRoomId = uuidv4().slice(0, 8);
    router.push(`/room/${newRoomId}`);
  };

  const joinExistingRoom = () => {
    if (roomId.trim()) {
      router.push(`/room/${roomId.trim()}`);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      joinExistingRoom();
    }
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-300 rounded-2xl mb-4">
            <Users className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-headline font-bold text-gray-900 mb-2">
            Scrum Poker Online
          </h1>
          <p className="text-gray-600 font-body">
            Collaborative story point estimation for agile teams
          </p>
        </div>

        {/* Actions */}
        <div className="space-y-4">
          {/* Create New Room */}
          <button
            onClick={createNewRoom}
            className="w-full bg-primary-300 hover:bg-primary-400 text-white font-medium py-4 px-6 rounded-xl transition-colors duration-200 flex items-center justify-center gap-3 shadow-soft hover:shadow-medium"
          >
            <Plus className="w-5 h-5" />
            Create New Room
          </button>

          {/* Divider */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-200"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-4 bg-background text-gray-500">or</span>
            </div>
          </div>

          {/* Join Existing Room */}
          <div className="space-y-3">
            <label htmlFor="roomId" className="block text-sm font-medium text-gray-700">
              Join Existing Room
            </label>
            <div className="flex gap-2">
              <input
                id="roomId"
                type="text"
                value={roomId}
                onChange={(e) => setRoomId(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Enter room ID"
                className="flex-1 px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-300 focus:border-transparent transition-colors"
              />
              <button
                onClick={joinExistingRoom}
                disabled={!roomId.trim()}
                className="px-4 py-3 bg-accent-300 hover:bg-accent-400 disabled:bg-gray-200 disabled:cursor-not-allowed text-white rounded-xl transition-colors duration-200 flex items-center justify-center"
              >
                <ArrowRight className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Features */}
        <div className="mt-12 grid grid-cols-1 gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-primary-300 rounded-full"></div>
            <span>Real-time collaborative estimation</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-primary-300 rounded-full"></div>
            <span>Automatic statistical calculations</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-primary-300 rounded-full"></div>
            <span>Smart probing questions for consensus</span>
          </div>
        </div>
      </div>
    </div>
  );
}
