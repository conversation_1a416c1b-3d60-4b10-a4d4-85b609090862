import { NextRequest } from 'next/server';
import { Server as NetServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import { v4 as uuidv4 } from 'uuid';
import type {
  ServerToClientEvents,
  ClientToServerEvents,
  InterServerEvents,
  SocketData,
  Room,
  User,
  Story,
  Estimate
} from '@/types';
import { generateProbingQuestions } from '@/utils/statistics';

// In-memory storage (in production, use Redis or a database)
const rooms = new Map<string, Room>();
const userSockets = new Map<string, string>(); // userId -> socketId

export async function GET(req: NextRequest) {
  if (!(global as any).io) {
    console.log('Initializing Socket.io server...');
    
    const httpServer: NetServer = (req as any).socket?.server;
    const io = new SocketIOServer<
      ClientToServerEvents,
      ServerToClientEvents,
      InterServerEvents,
      SocketData
    >(httpServer, {
      path: '/api/socket',
      addTrailingSlash: false,
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    });

    io.on('connection', (socket) => {
      console.log('User connected:', socket.id);

      socket.on('room:join', (roomId: string, userName: string) => {
        const userId = uuidv4();
        
        // Store user data in socket
        socket.data.userId = userId;
        socket.data.userName = userName;
        socket.data.roomId = roomId;
        
        // Track user socket
        userSockets.set(userId, socket.id);
        
        // Join socket room
        socket.join(roomId);
        
        // Get or create room
        let room = rooms.get(roomId);
        if (!room) {
          room = {
            id: roomId,
            name: `Room ${roomId.slice(0, 8)}`,
            users: [],
            stories: [],
            currentStoryId: null,
            createdAt: new Date()
          };
          rooms.set(roomId, room);
        }
        
        // Add user to room
        const user: User = {
          id: userId,
          name: userName,
          isConnected: true
        };
        
        room.users.push(user);
        rooms.set(roomId, room);
        
        // Notify all users in room
        io.to(roomId).emit('room:updated', room);
        socket.to(roomId).emit('user:joined', user);
        
        console.log(`User ${userName} (${userId}) joined room ${roomId}`);
      });

      socket.on('room:leave', (roomId: string) => {
        handleUserLeave(socket, roomId);
      });

      socket.on('story:add', (roomId: string, storyData) => {
        const room = rooms.get(roomId);
        if (!room) return;
        
        const story: Story = {
          id: uuidv4(),
          title: storyData.title,
          description: storyData.description,
          estimates: {},
          isRevealed: false,
          createdAt: new Date()
        };
        
        room.stories.push(story);
        rooms.set(roomId, room);
        
        io.to(roomId).emit('story:added', story);
        io.to(roomId).emit('room:updated', room);
      });

      socket.on('story:select', (roomId: string, storyId: string) => {
        const room = rooms.get(roomId);
        if (!room) return;
        
        room.currentStoryId = storyId;
        rooms.set(roomId, room);
        
        io.to(roomId).emit('room:updated', room);
      });

      socket.on('estimate:submit', (roomId: string, storyId: string, estimate: number | null) => {
        const room = rooms.get(roomId);
        const userId = socket.data.userId;
        if (!room || !userId) return;
        
        const story = room.stories.find(s => s.id === storyId);
        if (!story) return;
        
        story.estimates[userId] = estimate;
        rooms.set(roomId, room);
        
        const estimateData: Estimate = {
          userId,
          storyId,
          value: estimate
        };
        
        io.to(roomId).emit('estimate:submitted', estimateData);
        io.to(roomId).emit('room:updated', room);
      });

      socket.on('estimates:reveal', (roomId: string, storyId: string) => {
        const room = rooms.get(roomId);
        if (!room) return;
        
        const story = room.stories.find(s => s.id === storyId);
        if (!story) return;
        
        story.isRevealed = true;
        rooms.set(roomId, room);
        
        io.to(roomId).emit('estimates:revealed', storyId);
        io.to(roomId).emit('room:updated', room);
      });

      socket.on('estimates:reset', (roomId: string, storyId: string) => {
        const room = rooms.get(roomId);
        if (!room) return;

        const story = room.stories.find(s => s.id === storyId);
        if (!story) return;

        story.estimates = {};
        story.isRevealed = false;
        rooms.set(roomId, room);

        io.to(roomId).emit('room:updated', room);
      });

      socket.on('probing:request', (roomId: string, storyId: string) => {
        const room = rooms.get(roomId);
        if (!room) return;

        const story = room.stories.find(s => s.id === storyId);
        if (!story) return;

        const estimates = Object.values(story.estimates).filter(e => e !== null) as number[];
        if (estimates.length === 0) return;

        const questions = generateProbingQuestions(estimates, story.title);
        io.to(roomId).emit('probing:questions', questions);
      });

      socket.on('disconnect', () => {
        console.log('User disconnected:', socket.id);
        const roomId = socket.data.roomId;
        if (roomId) {
          handleUserLeave(socket, roomId);
        }
      });
    });

    function handleUserLeave(socket: any, roomId: string) {
      const userId = socket.data.userId;
      if (!userId) return;
      
      const room = rooms.get(roomId);
      if (!room) return;
      
      // Remove user from room
      room.users = room.users.filter(u => u.id !== userId);
      
      // Clean up empty rooms
      if (room.users.length === 0) {
        rooms.delete(roomId);
      } else {
        rooms.set(roomId, room);
        io.to(roomId).emit('room:updated', room);
        socket.to(roomId).emit('user:left', userId);
      }
      
      // Clean up user socket tracking
      userSockets.delete(userId);
      
      socket.leave(roomId);
    }

    (global as any).io = io;
  }

  return new Response('Socket.io server initialized', { status: 200 });
}
