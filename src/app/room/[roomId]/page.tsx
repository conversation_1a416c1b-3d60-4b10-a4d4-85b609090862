'use client';

import { useParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import { useSocket } from '@/hooks/useSocket';
import UserJoinForm from '@/components/UserJoinForm';
import RoomHeader from '@/components/RoomHeader';
import StoryList from '@/components/StoryList';
import EstimationArea from '@/components/EstimationArea';
import UserPresence from '@/components/UserPresence';
import ProbingQuestions from '@/components/ProbingQuestions';
import type { ProbingQuestion } from '@/types';

export default function RoomPage() {
  const params = useParams();
  const roomId = params.roomId as string;
  const [hasJoined, setHasJoined] = useState(false);
  const [probingQuestions, setProbingQuestions] = useState<ProbingQuestion[]>([]);
  
  const {
    isConnected,
    room,
    currentUser,
    error,
    probingQuestions,
    setProbingQuestions,
    joinRoom,
    leaveRoom,
    addStory,
    selectStory,
    submitEstimate,
    revealEstimates,
    resetEstimates,
    requestProbingQuestions,
  } = useSocket();

  useEffect(() => {
    // Initialize socket connection
    fetch('/api/socket');
  }, []);

  useEffect(() => {
    return () => {
      if (hasJoined && roomId) {
        leaveRoom(roomId);
      }
    };
  }, [hasJoined, roomId, leaveRoom]);

  const handleJoinRoom = (userName: string) => {
    joinRoom(roomId, userName);
    setHasJoined(true);
  };

  const handleAddStory = (title: string, description: string) => {
    addStory(roomId, title, description);
  };

  const handleSelectStory = (storyId: string) => {
    selectStory(roomId, storyId);
  };

  const handleSubmitEstimate = (storyId: string, estimate: number | null) => {
    submitEstimate(roomId, storyId, estimate);
  };

  const handleRevealEstimates = (storyId: string) => {
    revealEstimates(roomId, storyId);
  };

  const handleResetEstimates = (storyId: string) => {
    resetEstimates(roomId, storyId);
  };

  const handleRequestProbing = (storyId: string) => {
    requestProbingQuestions(roomId, storyId);
  };

  if (!isConnected) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-300 mx-auto mb-4"></div>
          <p className="text-gray-600">Connecting to server...</p>
          {error && (
            <p className="text-error mt-2">{error}</p>
          )}
        </div>
      </div>
    );
  }

  if (!hasJoined || !currentUser) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <UserJoinForm 
          roomId={roomId}
          onJoin={handleJoinRoom}
          error={error}
        />
      </div>
    );
  }

  if (!room) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-pulse-soft">
            <div className="h-8 bg-gray-200 rounded w-48 mx-auto mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-32 mx-auto"></div>
          </div>
          <p className="text-gray-600 mt-4">Loading room...</p>
        </div>
      </div>
    );
  }

  const currentStory = room.currentStoryId 
    ? room.stories.find(s => s.id === room.currentStoryId)
    : null;

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-6">
        <RoomHeader 
          room={room}
          currentUser={currentUser}
          onLeaveRoom={() => {
            leaveRoom(roomId);
            setHasJoined(false);
          }}
        />
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
          {/* Left Column: Stories */}
          <div className="lg:col-span-1">
            <StoryList
              stories={room.stories}
              currentStoryId={room.currentStoryId}
              onAddStory={handleAddStory}
              onSelectStory={handleSelectStory}
            />
          </div>
          
          {/* Middle Column: Estimation Area */}
          <div className="lg:col-span-1">
            {currentStory ? (
              <EstimationArea
                story={currentStory}
                users={room.users}
                currentUser={currentUser}
                onSubmitEstimate={handleSubmitEstimate}
                onRevealEstimates={handleRevealEstimates}
                onResetEstimates={handleResetEstimates}
                onRequestProbing={handleRequestProbing}
              />
            ) : (
              <div className="bg-white rounded-2xl p-6 shadow-soft text-center">
                <p className="text-gray-500">Select a story to start estimating</p>
              </div>
            )}
          </div>
          
          {/* Right Column: User Presence */}
          <div className="lg:col-span-1">
            <UserPresence 
              users={room.users}
              currentUser={currentUser}
            />
          </div>
        </div>
      </div>

      {/* Probing Questions Modal */}
      {probingQuestions.length > 0 && (
        <ProbingQuestions
          questions={probingQuestions}
          onClose={() => setProbingQuestions([])}
        />
      )}
    </div>
  );
}
