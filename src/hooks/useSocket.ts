'use client';

import { useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import type { 
  ServerToClientEvents, 
  ClientToServerEvents,
  Room,
  User,
  Story,
  Estimate,
  ProbingQuestion
} from '@/types';

type SocketType = Socket<ServerToClientEvents, ClientToServerEvents>;

export function useSocket() {
  const [socket, setSocket] = useState<SocketType | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [room, setRoom] = useState<Room | null>(null);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [probingQuestions, setProbingQuestions] = useState<ProbingQuestion[]>([]);

  useEffect(() => {
    // Initialize socket connection
    const socketInstance: SocketType = io({
      path: '/api/socket',
      addTrailingSlash: false,
    });

    // Connection event handlers
    socketInstance.on('connect', () => {
      console.log('Connected to server');
      setIsConnected(true);
      setError(null);
    });

    socketInstance.on('disconnect', () => {
      console.log('Disconnected from server');
      setIsConnected(false);
    });

    socketInstance.on('connect_error', (err) => {
      console.error('Connection error:', err);
      setError('Failed to connect to server');
      setIsConnected(false);
    });

    // Room event handlers
    socketInstance.on('room:updated', (updatedRoom: Room) => {
      setRoom(updatedRoom);
    });

    socketInstance.on('user:joined', (user: User) => {
      console.log('User joined:', user.name);
    });

    socketInstance.on('user:left', (userId: string) => {
      console.log('User left:', userId);
    });

    socketInstance.on('story:added', (story: Story) => {
      console.log('Story added:', story.title);
    });

    socketInstance.on('story:updated', (story: Story) => {
      console.log('Story updated:', story.title);
    });

    socketInstance.on('estimate:submitted', (estimate: Estimate) => {
      console.log('Estimate submitted:', estimate);
    });

    socketInstance.on('estimates:revealed', (storyId: string) => {
      console.log('Estimates revealed for story:', storyId);
    });

    socketInstance.on('probing:questions', (questions: ProbingQuestion[]) => {
      console.log('Probing questions received:', questions);
      setProbingQuestions(questions);
    });

    socketInstance.on('error', (message: string) => {
      console.error('Socket error:', message);
      setError(message);
    });

    setSocket(socketInstance);

    return () => {
      socketInstance.disconnect();
    };
  }, []);

  // Socket action methods
  const joinRoom = (roomId: string, userName: string) => {
    if (!socket) return;
    
    socket.emit('room:join', roomId, userName);
    setCurrentUser({ id: '', name: userName, isConnected: true });
  };

  const leaveRoom = (roomId: string) => {
    if (!socket) return;
    
    socket.emit('room:leave', roomId);
    setRoom(null);
    setCurrentUser(null);
  };

  const addStory = (roomId: string, title: string, description: string) => {
    if (!socket) return;
    
    socket.emit('story:add', roomId, { title, description });
  };

  const selectStory = (roomId: string, storyId: string) => {
    if (!socket) return;
    
    socket.emit('story:select', roomId, storyId);
  };

  const submitEstimate = (roomId: string, storyId: string, estimate: number | null) => {
    if (!socket) return;
    
    socket.emit('estimate:submit', roomId, storyId, estimate);
  };

  const revealEstimates = (roomId: string, storyId: string) => {
    if (!socket) return;
    
    socket.emit('estimates:reveal', roomId, storyId);
  };

  const resetEstimates = (roomId: string, storyId: string) => {
    if (!socket) return;
    
    socket.emit('estimates:reset', roomId, storyId);
  };

  const requestProbingQuestions = (roomId: string, storyId: string) => {
    if (!socket) return;
    
    socket.emit('probing:request', roomId, storyId);
  };

  return {
    socket,
    isConnected,
    room,
    currentUser,
    error,
    probingQuestions,
    setProbingQuestions,
    joinRoom,
    leaveRoom,
    addStory,
    selectStory,
    submitEstimate,
    revealEstimates,
    resetEstimates,
    requestProbingQuestions,
  };
}
