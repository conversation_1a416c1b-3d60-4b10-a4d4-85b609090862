import type { EstimateStats, ProbingQuestion } from '@/types';

/**
 * Calculate statistical measures for a set of estimates
 */
export function calculateEstimateStats(estimates: number[]): EstimateStats {
  if (estimates.length === 0) {
    return {
      mean: 0,
      median: 0,
      mode: [],
      variance: 0,
      standardDeviation: 0,
      estimates: []
    };
  }

  const sortedEstimates = [...estimates].sort((a, b) => a - b);
  
  // Calculate mean
  const mean = estimates.reduce((sum, val) => sum + val, 0) / estimates.length;
  
  // Calculate median
  const median = sortedEstimates.length % 2 === 0
    ? (sortedEstimates[sortedEstimates.length / 2 - 1] + sortedEstimates[sortedEstimates.length / 2]) / 2
    : sortedEstimates[Math.floor(sortedEstimates.length / 2)];
  
  // Calculate mode
  const frequency: Record<number, number> = {};
  estimates.forEach(val => {
    frequency[val] = (frequency[val] || 0) + 1;
  });
  
  const maxFrequency = Math.max(...Object.values(frequency));
  const mode = Object.keys(frequency)
    .filter(key => frequency[Number(key)] === maxFrequency)
    .map(Number);
  
  // Calculate variance
  const variance = estimates.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / estimates.length;
  
  // Calculate standard deviation
  const standardDeviation = Math.sqrt(variance);
  
  return {
    mean: Math.round(mean * 100) / 100,
    median,
    mode,
    variance: Math.round(variance * 100) / 100,
    standardDeviation: Math.round(standardDeviation * 100) / 100,
    estimates: sortedEstimates
  };
}

/**
 * Check if estimates show significant divergence
 */
export function hasSignificantDivergence(estimates: number[], threshold: number = 2): boolean {
  if (estimates.length < 2) return false;
  
  const stats = calculateEstimateStats(estimates);
  const coefficientOfVariation = stats.standardDeviation / stats.mean;
  
  // Consider divergent if coefficient of variation > threshold or 
  // if the range spans more than 3 fibonacci numbers
  const range = Math.max(...estimates) - Math.min(...estimates);
  const fibonacciSpan = getFibonacciSpan(estimates);
  
  return coefficientOfVariation > threshold || fibonacciSpan > 3;
}

/**
 * Calculate how many fibonacci numbers the estimates span
 */
function getFibonacciSpan(estimates: number[]): number {
  const fibSequence = [0, 0.5, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89];
  const min = Math.min(...estimates);
  const max = Math.max(...estimates);
  
  const minIndex = fibSequence.findIndex(val => val >= min);
  const maxIndex = fibSequence.findIndex(val => val >= max);
  
  if (minIndex === -1 || maxIndex === -1) return 0;
  
  return maxIndex - minIndex;
}

/**
 * Generate probing questions based on estimate divergence
 */
export function generateProbingQuestions(estimates: number[], storyTitle: string): ProbingQuestion[] {
  const stats = calculateEstimateStats(estimates);
  const questions: ProbingQuestion[] = [];
  
  // High variance questions
  if (stats.standardDeviation > 5) {
    questions.push({
      id: 'complexity-understanding',
      question: `There's significant disagreement on "${storyTitle}". What aspects of this story might be more complex than initially apparent?`,
      category: 'complexity'
    });
    
    questions.push({
      id: 'scope-clarity',
      question: `Are we all aligned on the scope of "${storyTitle}"? What might be included or excluded?`,
      category: 'scope'
    });
  }
  
  // Large range questions
  const range = Math.max(...estimates) - Math.min(...estimates);
  if (range > 8) {
    questions.push({
      id: 'uncertainty-factors',
      question: `The estimates range from ${Math.min(...estimates)} to ${Math.max(...estimates)}. What unknowns or risks might explain this difference?`,
      category: 'uncertainty'
    });
    
    questions.push({
      id: 'dependencies-check',
      question: `Are there external dependencies or integrations that might affect the effort for "${storyTitle}"?`,
      category: 'dependencies'
    });
  }
  
  // Low estimates vs high estimates
  const lowEstimates = estimates.filter(e => e <= stats.median);
  const highEstimates = estimates.filter(e => e > stats.median);
  
  if (lowEstimates.length > 0 && highEstimates.length > 0) {
    questions.push({
      id: 'approach-differences',
      question: `Those with lower estimates: what approach are you considering? Those with higher estimates: what additional work do you foresee?`,
      category: 'complexity'
    });
  }
  
  // Very high estimates
  if (Math.max(...estimates) > 21) {
    questions.push({
      id: 'breakdown-suggestion',
      question: `Some estimates are quite high. Should "${storyTitle}" be broken down into smaller, more manageable pieces?`,
      category: 'scope'
    });
  }
  
  return questions;
}

/**
 * Format estimate statistics for display
 */
export function formatStats(stats: EstimateStats): string {
  const { mean, median, mode } = stats;
  
  let result = `Mean: ${mean}, Median: ${median}`;
  
  if (mode.length === 1) {
    result += `, Mode: ${mode[0]}`;
  } else if (mode.length > 1 && mode.length < stats.estimates.length) {
    result += `, Modes: ${mode.join(', ')}`;
  }
  
  return result;
}

/**
 * Get the closest fibonacci number to a given value
 */
export function getClosestFibonacci(value: number): number {
  const fibSequence = [0, 0.5, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89];
  
  return fibSequence.reduce((prev, curr) => 
    Math.abs(curr - value) < Math.abs(prev - value) ? curr : prev
  );
}
