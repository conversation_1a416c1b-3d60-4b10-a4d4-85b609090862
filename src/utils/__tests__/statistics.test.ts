import { 
  calculateEstimateStats, 
  hasSignificantDivergence, 
  generateProbingQuestions,
  getClosest<PERSON><PERSON><PERSON>cci 
} from '../statistics';

describe('Statistics Utils', () => {
  describe('calculateEstimateStats', () => {
    it('should calculate correct statistics for a set of estimates', () => {
      const estimates = [1, 2, 3, 5, 8];
      const stats = calculateEstimateStats(estimates);
      
      expect(stats.mean).toBe(3.8);
      expect(stats.median).toBe(3);
      expect(stats.estimates).toEqual([1, 2, 3, 5, 8]);
    });

    it('should handle empty estimates array', () => {
      const stats = calculateEstimateStats([]);
      
      expect(stats.mean).toBe(0);
      expect(stats.median).toBe(0);
      expect(stats.mode).toEqual([]);
      expect(stats.estimates).toEqual([]);
    });

    it('should calculate mode correctly', () => {
      const estimates = [1, 2, 2, 3, 3, 3];
      const stats = calculateEstimateStats(estimates);
      
      expect(stats.mode).toEqual([3]);
    });

    it('should handle multiple modes', () => {
      const estimates = [1, 1, 2, 2, 3];
      const stats = calculateEstimateStats(estimates);
      
      expect(stats.mode).toContain(1);
      expect(stats.mode).toContain(2);
    });
  });

  describe('hasSignificantDivergence', () => {
    it('should detect significant divergence', () => {
      const estimates = [1, 13, 21]; // Large spread
      expect(hasSignificantDivergence(estimates)).toBe(true);
    });

    it('should not detect divergence for similar estimates', () => {
      const estimates = [3, 5, 5, 8]; // Close estimates
      expect(hasSignificantDivergence(estimates)).toBe(false);
    });

    it('should handle single estimate', () => {
      const estimates = [5];
      expect(hasSignificantDivergence(estimates)).toBe(false);
    });
  });

  describe('generateProbingQuestions', () => {
    it('should generate questions for high variance estimates', () => {
      const estimates = [1, 13, 21];
      const questions = generateProbingQuestions(estimates, 'Test Story');
      
      expect(questions.length).toBeGreaterThan(0);
      expect(questions.some(q => q.category === 'complexity')).toBe(true);
    });

    it('should generate different questions for different scenarios', () => {
      const estimates = [1, 34]; // Very high estimate
      const questions = generateProbingQuestions(estimates, 'Complex Story');
      
      expect(questions.some(q => q.category === 'scope')).toBe(true);
    });
  });

  describe('getClosestFibonacci', () => {
    it('should return closest fibonacci number', () => {
      expect(getClosestFibonacci(4)).toBe(3);
      expect(getClosestFibonacci(6)).toBe(5);
      expect(getClosestFibonacci(10)).toBe(8);
    });

    it('should handle exact matches', () => {
      expect(getClosestFibonacci(5)).toBe(5);
      expect(getClosestFibonacci(13)).toBe(13);
    });
  });
});

// Integration test for the complete flow
describe('Estimation Flow Integration', () => {
  it('should handle complete estimation workflow', () => {
    const estimates = [2, 8, 13, 21];
    
    // Calculate stats
    const stats = calculateEstimateStats(estimates);
    expect(stats.mean).toBeGreaterThan(0);
    
    // Check for divergence
    const hasDivergence = hasSignificantDivergence(estimates);
    expect(typeof hasDivergence).toBe('boolean');
    
    // Generate questions if divergent
    if (hasDivergence) {
      const questions = generateProbingQuestions(estimates, 'Test Story');
      expect(questions.length).toBeGreaterThan(0);
      expect(questions.every(q => q.question.length > 0)).toBe(true);
    }
  });
});
